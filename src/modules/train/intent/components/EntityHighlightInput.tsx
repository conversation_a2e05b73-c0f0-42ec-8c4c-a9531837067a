import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { Entity } from '@/types';
import { useGetEntitiesQuery } from '@/store/api';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Trash2 } from 'lucide-react';

interface EntityHighlightInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  botId: string;
  intentId?: string;
}

interface ParsedEntity {
  start: number;
  end: number;
  text: string;
  name: string;
  id: string;
  type: string;
}

interface HoveredEntity {
  entityId: string;
  entityName: string;
  entityType: string;
  x: number;
  y: number;
}

const EntityHighlightInput: React.FC<EntityHighlightInputProps> = ({
  value,
  onChange,
  placeholder = 'Enter utterance text...',
  className,
  botId,
  intentId,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [selectionStart, setSelectionStart] = useState<number>(0);
  const [selectionEnd, setSelectionEnd] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredEntity, setHoveredEntity] = useState<HoveredEntity | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [undoStack, setUndoStack] = useState<string[]>([]);
  const [redoStack, setRedoStack] = useState<string[]>([]);
  const [isUndoRedo, setIsUndoRedo] = useState(false);
  const contentEditableRef = useRef<HTMLDivElement>(null);

  // Parse existing entities from the text
  const parseEntities = useCallback((text: string): ParsedEntity[] => {
    const entityRegex = /\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g;
    const entities: ParsedEntity[] = [];

    let match;
    while ((match = entityRegex.exec(text)) !== null) {
      entities.push({
        start: match.index,
        end: match.index + match[0].length,
        text: match[1],
        name: match[2],
        id: match[3],
        type: match[4],
      });
    }

    return entities.sort((a, b) => a.start - b.start);
  }, []);

  // Get display text (without markup)
  const getDisplayText = useCallback((text: string) => {
    return text.replace(/\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g, '$1');
  }, []);

  // Get entity color based on type
  const getEntityColor = useCallback((entityType: string) => {
    const colors = {
      TEXT: 'bg-blue-100 text-blue-800 border-blue-200',
      EMAIL: 'bg-green-100 text-green-800 border-green-200',
      DATE: 'bg-purple-100 text-purple-800 border-purple-200',
      NUMBER: 'bg-orange-100 text-orange-800 border-orange-200',
      REGEX: 'bg-pink-100 text-pink-800 border-pink-200',
    };
    return colors[entityType as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  }, []);

  // Check for entity overlaps in display text coordinates
  const checkEntityOverlap = useCallback(
    (newStart: number, newEnd: number, existingEntities: ParsedEntity[], displayText: string) => {
      for (const entity of existingEntities) {
        const entityDisplayStart = getDisplayText(value.substring(0, entity.start)).length;
        const entityDisplayEnd = entityDisplayStart + entity.text.length;

        // Check for any overlap (including partial overlaps)
        if (
          (newStart < entityDisplayEnd && newEnd > entityDisplayStart) || // Any overlap
          (newStart >= entityDisplayStart && newStart < entityDisplayEnd) || // New start inside existing
          (newEnd > entityDisplayStart && newEnd <= entityDisplayEnd) || // New end inside existing
          (newStart <= entityDisplayStart && newEnd >= entityDisplayEnd) // New encompasses existing
        ) {
          return {
            hasOverlap: true,
            conflictingEntity: entity,
            conflictingText: displayText.substring(entityDisplayStart, entityDisplayEnd),
          };
        }
      }
      return { hasOverlap: false };
    },
    [value, getDisplayText]
  );

  // Add to undo stack when value changes (but not during undo/redo operations)
  const addToUndoStack = useCallback(
    (newValue: string) => {
      if (!isUndoRedo && value !== newValue) {
        setUndoStack(prev => [...prev.slice(-19), value]); // Keep last 20 states
        setRedoStack([]); // Clear redo stack when new action is performed
      }
    },
    [value, isUndoRedo]
  );

  // Undo functionality
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;

    setIsUndoRedo(true);
    const previousValue = undoStack[undoStack.length - 1];
    setRedoStack(prev => [...prev, value]);
    setUndoStack(prev => prev.slice(0, -1));
    onChange(previousValue);

    // Reset undo/redo flag after state update
    setTimeout(() => setIsUndoRedo(false), 0);
  }, [undoStack, value, onChange]);

  // Redo functionality
  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;

    setIsUndoRedo(true);
    const nextValue = redoStack[redoStack.length - 1];
    setUndoStack(prev => [...prev, value]);
    setRedoStack(prev => prev.slice(0, -1));
    onChange(nextValue);

    // Reset undo/redo flag after state update
    setTimeout(() => setIsUndoRedo(false), 0);
  }, [redoStack, value, onChange]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        if (e.key === 'z' && !e.shiftKey) {
          e.preventDefault();
          handleUndo();
        } else if ((e.key === 'z' && e.shiftKey) || e.key === 'y') {
          e.preventDefault();
          handleRedo();
        }
      }
    },
    [handleUndo, handleRedo]
  );

  // Find word boundaries and expand selection to complete words
  const expandToWordBoundaries = useCallback((text: string, start: number, end: number) => {
    // Word boundary regex - matches spaces, punctuation, start/end of string
    const wordBoundaryRegex = /[\s\p{P}\p{S}]/u;

    // Find start of word by going backwards
    let wordStart = start;
    while (wordStart > 0 && !wordBoundaryRegex.test(text[wordStart - 1])) {
      wordStart--;
    }

    // Find end of word by going forwards
    let wordEnd = end;
    while (wordEnd < text.length && !wordBoundaryRegex.test(text[wordEnd])) {
      wordEnd++;
    }

    // If selection spans multiple words, find boundaries of all included words
    if (wordStart < start || wordEnd > end) {
      // Check if there are word boundaries within the original selection
      let hasInternalBoundary = false;
      for (let i = start; i < end; i++) {
        if (wordBoundaryRegex.test(text[i])) {
          hasInternalBoundary = true;
          break;
        }
      }

      // If selection spans multiple words, expand to include all partial words
      if (hasInternalBoundary) {
        // Find the start of the first word
        while (wordStart > 0 && !wordBoundaryRegex.test(text[wordStart - 1])) {
          wordStart--;
        }

        // Find the end of the last word
        while (wordEnd < text.length && !wordBoundaryRegex.test(text[wordEnd])) {
          wordEnd++;
        }
      }
    }

    return {
      start: wordStart,
      end: wordEnd,
      text: text.substring(wordStart, wordEnd),
      wasExpanded: wordStart !== start || wordEnd !== end,
    };
  }, []);

  // Render content with inline entity highlights for contentEditable
  const renderContentWithEntities = useCallback(() => {
    const entities = parseEntities(value);
    const displayText = getDisplayText(value);

    if (entities.length === 0) {
      return displayText;
    }

    const parts: string[] = [];
    let lastEnd = 0;

    // Map entities to their display positions
    const displayEntities = entities.map(entity => {
      const displayStart = getDisplayText(value.substring(0, entity.start)).length;
      const displayEnd = displayStart + entity.text.length;
      return {
        ...entity,
        displayStart,
        displayEnd,
      };
    });

    displayEntities.forEach(entity => {
      // Add text before entity
      if (entity.displayStart > lastEnd) {
        parts.push(displayText.substring(lastEnd, entity.displayStart));
      }

      // Add entity span with proper styling and data attributes
      const entityColor = getEntityColor(entity.type);
      parts.push(
        `<span class="${entityColor} border rounded px-1 cursor-pointer inline-block whitespace-nowrap" data-entity-id="${entity.id}" data-entity-name="${entity.name}" data-entity-type="${entity.type}">${entity.text}</span>`
      );

      lastEnd = entity.displayEnd;
    });

    // Add remaining text
    if (lastEnd < displayText.length) {
      parts.push(displayText.substring(lastEnd));
    }

    return parts.join('');
  }, [value, parseEntities, getDisplayText, getEntityColor]);

  // Save cursor position in display text coordinates with improved accuracy
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) {
      return { position: 0, node: null, offset: 0 };
    }

    const range = selection.getRangeAt(0);
    if (!contentEditableRef.current.contains(range.startContainer)) {
      return { position: 0, node: null, offset: 0 };
    }

    // Store both the text position and the actual DOM node/offset for more precise restoration
    const beforeRange = range.cloneRange();
    beforeRange.selectNodeContents(contentEditableRef.current);
    beforeRange.setEnd(range.startContainer, range.startOffset);

    return {
      position: beforeRange.toString().length,
      node: range.startContainer,
      offset: range.startOffset,
    };
  }, []);

  // Restore cursor position with improved precision
  const restoreCursorPosition = useCallback(
    (savedPosition: { position: number; node: Node | null; offset: number } | number) => {
      if (!contentEditableRef.current) return;

      // Handle legacy number format for backward compatibility
      if (typeof savedPosition === 'number') {
        if (savedPosition <= 0) return;

        try {
          const walker = document.createTreeWalker(
            contentEditableRef.current,
            NodeFilter.SHOW_TEXT,
            null
          );

          let currentPos = 0;
          let textNode = walker.nextNode();

          while (textNode) {
            const nodeText = textNode.textContent || '';
            const nodeEnd = currentPos + nodeText.length;

            if (savedPosition <= nodeEnd) {
              const newRange = document.createRange();
              const newSelection = window.getSelection();
              const offset = Math.min(savedPosition - currentPos, nodeText.length);

              newRange.setStart(textNode, offset);
              newRange.collapse(true);
              newSelection?.removeAllRanges();
              newSelection?.addRange(newRange);
              break;
            }

            currentPos = nodeEnd;
            textNode = walker.nextNode();
          }
        } catch (error) {
          console.warn('Failed to restore cursor position:', error);
        }
        return;
      }

      // Use the enhanced position object
      const { position, node, offset } = savedPosition;

      try {
        // First try to restore using the exact node and offset if the node still exists
        if (node && contentEditableRef.current.contains(node) && node.textContent) {
          // Additional safety check: ensure the node is still a text node
          if (node.nodeType === Node.TEXT_NODE) {
            const newRange = document.createRange();
            const newSelection = window.getSelection();
            const safeOffset = Math.min(offset, node.textContent.length);

            newRange.setStart(node, safeOffset);
            newRange.collapse(true);
            newSelection?.removeAllRanges();
            newSelection?.addRange(newRange);
            return;
          }
        }

        // Fallback to position-based restoration
        if (position > 0) {
          const walker = document.createTreeWalker(
            contentEditableRef.current,
            NodeFilter.SHOW_TEXT,
            null
          );

          let currentPos = 0;
          let textNode = walker.nextNode();

          while (textNode) {
            const nodeText = textNode.textContent || '';
            const nodeEnd = currentPos + nodeText.length;

            if (position <= nodeEnd) {
              const newRange = document.createRange();
              const newSelection = window.getSelection();
              const offset = Math.min(position - currentPos, nodeText.length);

              newRange.setStart(textNode, offset);
              newRange.collapse(true);
              newSelection?.removeAllRanges();
              newSelection?.addRange(newRange);
              break;
            }

            currentPos = nodeEnd;
            textNode = walker.nextNode();
          }
        }
      } catch (error) {
        console.warn('Failed to restore cursor position:', error);
      }
    },
    []
  );

  // Track if we're in the middle of a typing operation
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastValueRef = useRef<string>(value);
  const isInternalUpdateRef = useRef<boolean>(false);

  // Initialize contentEditable content only on mount or external value changes
  useEffect(() => {
    if (contentEditableRef.current) {
      // Only update if this is an external change (not from typing)
      const isExternalChange = !isInternalUpdateRef.current && value !== lastValueRef.current;

      if (isExternalChange || !contentEditableRef.current.innerHTML) {
        const expectedHTML = renderContentWithEntities();
        const currentHTML = contentEditableRef.current.innerHTML;

        if (currentHTML !== expectedHTML) {
          // Save cursor position before updating
          const cursorPosition = saveCursorPosition();
          contentEditableRef.current.innerHTML = expectedHTML;

          // Restore cursor position if it was saved
          if (cursorPosition.position > 0 || cursorPosition.node) {
            setTimeout(() => {
              restoreCursorPosition(cursorPosition);
            }, 0);
          }
        }
      }

      lastValueRef.current = value;
      isInternalUpdateRef.current = false;
    }
  }, [value, renderContentWithEntities, saveCursorPosition, restoreCursorPosition]);

  // Cleanup timeouts on unmount and when component updates
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    };
  }, []);

  // Additional cleanup when isTyping changes
  useEffect(() => {
    if (!isTyping && typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, [isTyping]);

  // Only sync contentEditable when explicitly needed (entity operations, undo/redo)
  const syncContentEditable = useCallback(() => {
    if (contentEditableRef.current) {
      const currentHTML = contentEditableRef.current.innerHTML;
      const expectedHTML = renderContentWithEntities();

      if (currentHTML !== expectedHTML) {
        // Save current cursor position in display text coordinates
        const cursorPosition = saveCursorPosition();

        // Update content
        contentEditableRef.current.innerHTML = expectedHTML;

        // Restore cursor position
        if (cursorPosition.position > 0 || cursorPosition.node) {
          // Use setTimeout to ensure DOM is updated
          setTimeout(() => {
            restoreCursorPosition(cursorPosition);
          }, 0);
        }
      }
    }
  }, [renderContentWithEntities, saveCursorPosition, restoreCursorPosition]);

  // Minimal sync - only when entities are involved
  useEffect(() => {
    if (!isUpdating && !isUndoRedo && !isTyping && !isInternalUpdateRef.current) {
      const contentEditable = contentEditableRef.current;
      if (contentEditable) {
        const entities = parseEntities(value);

        // Only sync if there are entities that need to be displayed
        if (entities.length > 0) {
          const hasEntitiesInDisplay = contentEditable.innerHTML.includes('<span');

          // Sync only if entities exist but aren't displayed
          if (!hasEntitiesInDisplay) {
            syncContentEditable();
          }
        }
      }
    }
  }, [value, isUpdating, isUndoRedo, isTyping, syncContentEditable, parseEntities]);

  // Fetch entities from API
  const { data: entitiesResponse, isLoading } = useGetEntitiesQuery({
    botId,
    page: 1,
    limit: 100,
  });

  // Filter entities based on intent and search term
  const filteredEntities = useMemo(() => {
    if (!entitiesResponse?.data?.items) return [];

    return entitiesResponse.data.items.filter((entity: Entity) => {
      // Show global entities (intentId is null) or entities specific to current intent
      const intentMatch = entity.intentId === null || entity.intentId === intentId;
      const searchMatch =
        searchTerm === '' ||
        entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entity.type.toLowerCase().includes(searchTerm.toLowerCase());
      return intentMatch && searchMatch;
    });
  }, [entitiesResponse, intentId, searchTerm]);

  // Handle text selection in contentEditable - simplified and more reliable
  const handleTextSelection = useCallback(() => {
    // Don't interfere with selection during typing
    if (isTyping) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      setShowDropdown(false);
      setSelectedText('');
      return;
    }

    const range = selection.getRangeAt(0);
    const selectedTextContent = selection.toString().trim();

    // Only proceed if there's actual text selected and it's within our contentEditable
    if (
      selectedTextContent &&
      selectedTextContent.length > 0 &&
      contentEditableRef.current?.contains(range.commonAncestorContainer)
    ) {
      // Calculate selection position in display text more accurately
      const contentEditable = contentEditableRef.current;
      const displayText = contentEditable.textContent || '';

      // Find the actual start position by walking through text nodes
      let actualStart = 0;
      let actualEnd = 0;

      try {
        // Create a range from the beginning to the selection start
        const beforeRange = range.cloneRange();
        beforeRange.selectNodeContents(contentEditable);
        beforeRange.setEnd(range.startContainer, range.startOffset);
        actualStart = beforeRange.toString().length;

        // Calculate end position
        actualEnd = actualStart + selectedTextContent.length;

        // Validate the selection matches what we expect
        const expectedText = displayText.substring(actualStart, actualEnd);
        if (expectedText === selectedTextContent) {
          // Expand selection to word boundaries only if it makes sense
          const expanded = expandToWordBoundaries(displayText, actualStart, actualEnd);

          setSelectedText(expanded.text);
          setSelectionStart(expanded.start);
          setSelectionEnd(expanded.end);
          setShowDropdown(true);
          setSearchTerm('');
        } else {
          // Fallback: use the actual selected text without expansion
          setSelectedText(selectedTextContent);
          setSelectionStart(actualStart);
          setSelectionEnd(actualEnd);
          setShowDropdown(true);
          setSearchTerm('');
        }
      } catch (error) {
        console.warn('Failed to calculate selection position:', error);
        // Fallback: use the selected text as-is
        setSelectedText(selectedTextContent);
        setSelectionStart(0);
        setSelectionEnd(selectedTextContent.length);
        setShowDropdown(true);
        setSearchTerm('');
      }
    } else {
      setShowDropdown(false);
      setSelectedText('');
    }
  }, [isTyping, expandToWordBoundaries]);

  // Handle entity selection for contentEditable
  const handleEntitySelect = useCallback(
    (entity: Entity) => {
      if (!selectedText || selectionStart === selectionEnd) return;

      setIsUpdating(true);
      setErrorMessage(''); // Clear any previous error

      try {
        const displayText = getDisplayText(value);
        const existingEntities = parseEntities(value);

        // Check for overlaps before proceeding
        const overlapCheck = checkEntityOverlap(
          selectionStart,
          selectionEnd,
          existingEntities,
          displayText
        );

        if (overlapCheck.hasOverlap) {
          const conflictingEntity = overlapCheck.conflictingEntity!;
          const conflictingText = overlapCheck.conflictingText!;

          setErrorMessage(
            `Cannot assign entity "${entity.name}" to "${selectedText}". ` +
              `The selected text overlaps with existing entity "${conflictingEntity.name}" (${conflictingText}).`
          );

          // Auto-clear error after 5 seconds
          setTimeout(() => setErrorMessage(''), 5000);
          return;
        }

        // Create entity markup
        const entityMarkup = `[${selectedText}](${entity.name}_${entity.id}_${entity.type})`;

        // Build new text with entity markup
        const beforeText = displayText.substring(0, selectionStart);
        const afterText = displayText.substring(selectionEnd);

        let newValue = '';

        if (existingEntities.length === 0) {
          // No existing entities, simple insertion
          newValue = beforeText + entityMarkup + afterText;
        } else {
          // Need to merge with existing entities
          const allParts: Array<{
            start: number;
            end: number;
            content: string;
            isEntity: boolean;
          }> = [];

          // Add existing entities
          existingEntities.forEach(existingEntity => {
            const entityDisplayStart = getDisplayText(
              value.substring(0, existingEntity.start)
            ).length;
            const entityDisplayEnd = entityDisplayStart + existingEntity.text.length;
            allParts.push({
              start: entityDisplayStart,
              end: entityDisplayEnd,
              content: `[${existingEntity.text}](${existingEntity.name}_${existingEntity.id}_${existingEntity.type})`,
              isEntity: true,
            });
          });

          // Add new entity
          allParts.push({
            start: selectionStart,
            end: selectionEnd,
            content: entityMarkup,
            isEntity: true,
          });

          // Sort by start position
          allParts.sort((a, b) => a.start - b.start);

          // Build final text
          let currentPos = 0;
          const finalParts: string[] = [];

          allParts.forEach(part => {
            // Add text before this part
            if (part.start > currentPos) {
              finalParts.push(displayText.substring(currentPos, part.start));
            }

            // Add the entity
            finalParts.push(part.content);
            currentPos = part.end;
          });

          // Add remaining text
          if (currentPos < displayText.length) {
            finalParts.push(displayText.substring(currentPos));
          }

          newValue = finalParts.join('');
        }

        // Add to undo stack before changing
        addToUndoStack(newValue);
        onChange(newValue);

        // Clear selection
        const selection = window.getSelection();
        selection?.removeAllRanges();

        // Force sync after entity addition
        setTimeout(() => {
          syncContentEditable();
        }, 0);
      } finally {
        setIsUpdating(false);
        setShowDropdown(false);
        setSelectedText('');
        setSelectionStart(0);
        setSelectionEnd(0);
      }
    },
    [
      selectedText,
      selectionStart,
      selectionEnd,
      value,
      onChange,
      getDisplayText,
      parseEntities,
      checkEntityOverlap,
      syncContentEditable,
    ]
  );

  // Handle content change in contentEditable - optimized for natural typing
  const handleContentChange = useCallback(() => {
    if (isUpdating || isUndoRedo) return; // Prevent recursive updates

    const contentEditable = contentEditableRef.current;
    if (!contentEditable) return;

    // Mark as typing to prevent content synchronization
    setIsTyping(true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to mark typing as finished
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 500); // Longer timeout to ensure stability

    // Get the current display text (without markup)
    const currentDisplayText = contentEditable.textContent || '';
    const previousDisplayText = getDisplayText(value);

    // Only proceed if the display text actually changed
    if (currentDisplayText === previousDisplayText) return;

    // Mark this as an internal update to prevent cursor jumping
    isInternalUpdateRef.current = true;

    // Always update with the current display text
    // This allows natural typing without entity interference
    if (currentDisplayText !== previousDisplayText) {
      addToUndoStack(value);
      onChange(currentDisplayText);
    }
  }, [onChange, isUpdating, isUndoRedo, value, addToUndoStack, getDisplayText]);

  // Handle entity hover in contentEditable
  const handleEntityHover = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.dataset.entityId && target.tagName === 'SPAN') {
      const rect = target.getBoundingClientRect();
      const containerRect = contentEditableRef.current?.getBoundingClientRect();

      if (containerRect) {
        setHoveredEntity({
          entityId: target.dataset.entityId,
          entityName: target.dataset.entityName || '',
          entityType: target.dataset.entityType || '',
          x: rect.left + rect.width / 2 - containerRect.left,
          y: rect.top - containerRect.top - 10,
        });
      }
    }
  }, []);

  // Handle entity hover leave
  const handleEntityHoverLeave = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    // Only clear hover if we're not moving to a child element
    if (!target.dataset.entityId) {
      setHoveredEntity(null);
    }
  }, []);

  // Close dropdown
  const closeDropdown = useCallback(() => {
    setShowDropdown(false);
    setSelectedText('');
    setSearchTerm('');
    setSelectionStart(0);
    setSelectionEnd(0);
  }, []);

  // Remove entity
  const removeEntity = useCallback(
    (entityId: string) => {
      const entityRegex = new RegExp(`\\[([^\\]]+)\\]\\([^_]+_${entityId}_[^)]+\\)`, 'g');
      const newValue = value.replace(entityRegex, '$1');
      onChange(newValue);
      setHoveredEntity(null);
    },
    [value, onChange]
  );

  if (isLoading) {
    return (
      <div
        className={cn('min-h-[40px] border border-gray-200 rounded-md p-2 bg-gray-50', className)}
      >
        <div className="text-gray-500 text-sm">Loading entities...</div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {/* ContentEditable div with inline entity highlights */}
      <div
        ref={contentEditableRef}
        contentEditable={true}
        tabIndex={0}
        onInput={handleContentChange}
        onMouseUp={handleTextSelection}
        onKeyUp={handleTextSelection}
        onKeyDown={handleKeyDown}
        onMouseOver={handleEntityHover}
        onMouseLeave={handleEntityHoverLeave}
        className="w-full h-[40px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white overflow-x-auto overflow-y-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        style={{
          fontSize: '14px',
          lineHeight: '1.5',
          direction: 'ltr',
          textAlign: 'left',
          unicodeBidi: 'normal',
          whiteSpace: 'nowrap',
          wordBreak: 'keep-all',
          scrollbarWidth: 'thin',
          scrollbarColor: '#cbd5e1 #f1f5f9',
        }}
        data-placeholder={placeholder}
        data-testid="entity-input"
        role="textbox"
        aria-label={placeholder}
        aria-multiline="false"
      />

      {/* Placeholder overlay when empty */}
      {!value && (
        <div className="absolute inset-0 px-3 py-2 text-gray-400 pointer-events-none flex items-center text-sm">
          {placeholder}
        </div>
      )}

      {/* Error message overlay */}
      {errorMessage && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded-md text-red-700 text-xs z-40">
          <div className="flex items-start gap-2">
            <X className="h-3 w-3 text-red-500 flex-shrink-0 mt-0.5" />
            <span>{errorMessage}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setErrorMessage('')}
              className="ml-auto h-4 w-4 p-0 text-red-500 hover:text-red-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          <div className="p-2 border-b border-gray-100">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                placeholder="Search entities..."
                className="pl-8 pr-8 h-8"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={closeDropdown}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            {selectedText && (
              <div className="mt-2 text-xs text-gray-600">
                Selected: &ldquo;<span className="font-medium">{selectedText}</span>&rdquo;
              </div>
            )}
          </div>

          <div className="max-h-40 overflow-y-auto">
            {filteredEntities.length === 0 ? (
              <div className="p-3 text-sm text-gray-500 text-center">No entities found</div>
            ) : (
              filteredEntities.map(entity => (
                <button
                  key={entity.id}
                  onClick={() => handleEntitySelect(entity)}
                  className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-50 last:border-b-0 focus:outline-none focus:bg-gray-50"
                >
                  <div className="font-medium text-sm">{entity.name}</div>
                  <div className="text-xs text-gray-500">{entity.type}</div>
                </button>
              ))
            )}
          </div>
        </div>
      )}

      {/* Entity management tooltip */}
      {hoveredEntity && (
        <div
          className="absolute z-50 bg-white border border-gray-200 rounded-md shadow-lg p-3 text-xs min-w-[120px]"
          style={{
            left: Math.max(10, Math.min(hoveredEntity.x, window.innerWidth - 150)),
            top: hoveredEntity.y - 50,
            transform: 'translateX(-50%)',
          }}
        >
          <div className="flex items-center justify-between gap-2 mb-1">
            <span className="font-medium text-gray-900">{hoveredEntity.entityName}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeEntity(hoveredEntity.entityId)}
              className="h-5 w-5 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
              title="Remove entity"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
          <div className="text-gray-500 text-xs">{hoveredEntity.entityType}</div>
          {/* Tooltip arrow */}
          <div
            className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"
            style={{ marginTop: '-1px' }}
          />
        </div>
      )}
    </div>
  );
};

export default EntityHighlightInput;
