import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi } from 'vitest';
import EntityHighlightInput from '../EntityHighlightInput';
import { trainApi } from '../../../../api/trainApi';

// Mock the API
vi.mock('../../../../api/trainApi', () => ({
  trainApi: {
    endpoints: {
      getEntities: {
        useQuery: vi.fn(() => ({
          data: { data: [] },
          isLoading: false,
        })),
      },
    },
  },
}));

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      [trainApi.reducerPath]: trainApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(trainApi.middleware),
  });
};

describe('EntityHighlightInput - Cursor Positioning', () => {
  it('should maintain cursor position during typing', async () => {
    const mockOnChange = vi.fn();
    const store = createMockStore();

    render(
      <Provider store={store}>
        <EntityHighlightInput
          value=""
          onChange={mockOnChange}
          placeholder="Type here"
          botId="test-bot"
          intentId="test-intent"
        />
      </Provider>
    );

    const input = screen.getByRole('textbox');
    
    // Focus the input
    input.focus();
    
    // Simulate typing by dispatching input events
    fireEvent.input(input, { target: { textContent: 'H' } });
    expect(mockOnChange).toHaveBeenCalledWith('H');
    
    fireEvent.input(input, { target: { textContent: 'He' } });
    expect(mockOnChange).toHaveBeenCalledWith('He');
    
    fireEvent.input(input, { target: { textContent: 'Hel' } });
    expect(mockOnChange).toHaveBeenCalledWith('Hel');
    
    fireEvent.input(input, { target: { textContent: 'Hell' } });
    expect(mockOnChange).toHaveBeenCalledWith('Hell');
    
    fireEvent.input(input, { target: { textContent: 'Hello' } });
    expect(mockOnChange).toHaveBeenCalledWith('Hello');
    
    // Verify the final text content
    expect(input.textContent).toBe('Hello');
  });

  it('should handle cursor position when inserting text in middle', async () => {
    const mockOnChange = vi.fn();
    const store = createMockStore();

    render(
      <Provider store={store}>
        <EntityHighlightInput
          value="Hello World"
          onChange={mockOnChange}
          placeholder="Type here"
          botId="test-bot"
          intentId="test-intent"
        />
      </Provider>
    );

    const input = screen.getByRole('textbox');
    
    // Simulate inserting text in the middle
    fireEvent.input(input, { target: { textContent: 'Hello Beautiful World' } });
    expect(mockOnChange).toHaveBeenCalledWith('Hello Beautiful World');
  });

  it('should preserve cursor position when content is updated externally', async () => {
    const mockOnChange = vi.fn();
    const store = createMockStore();

    const { rerender } = render(
      <Provider store={store}>
        <EntityHighlightInput
          value="Hello"
          onChange={mockOnChange}
          placeholder="Type here"
          botId="test-bot"
          intentId="test-intent"
        />
      </Provider>
    );

    const input = screen.getByRole('textbox');
    expect(input.textContent).toBe('Hello');

    // Update value externally
    rerender(
      <Provider store={store}>
        <EntityHighlightInput
          value="Hello World"
          onChange={mockOnChange}
          placeholder="Type here"
          botId="test-bot"
          intentId="test-intent"
        />
      </Provider>
    );

    expect(input.textContent).toBe('Hello World');
  });
});
