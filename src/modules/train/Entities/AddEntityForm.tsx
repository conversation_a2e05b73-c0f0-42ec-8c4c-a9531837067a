import React from 'react';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';
import { useCreateEntityMutation, useUpdateEntityMutation } from '@/store/api';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { FloatingField } from '@/components/ui/floating-label';

import RenderButtons from '../components/RenderButtons';
import { CreateEntityRequest, EntityType, Entity } from '@/types';
import { createEntitySchema, EntityFormValues } from '../schema';

interface AddEntityFormProps {
  onClose: () => void;
  botId: string;
  entity?: Entity;
}

const AddEntityForm: React.FC<AddEntityFormProps> = ({ onClose, botId, entity }) => {
  const { t } = useTranslation();
  const [createEntity] = useCreateEntityMutation();
  const [updateEntity] = useUpdateEntityMutation();
  const { toast } = useToast();

  const form = useForm<EntityFormValues>({
    resolver: zodResolver(createEntitySchema(t)),
    defaultValues: {
      name: entity?.name || '',
      type: entity?.type || EntityType.TEXT,
    },
  });

  const onSubmit = async (values: EntityFormValues) => {
    try {
      if (entity) {
        await updateEntity({
          id: entity.id,
          name: values.name,
          type: values.type,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('entities.entityUpdated')} />,
        });
      } else {
        const newEntity: CreateEntityRequest = {
          botId,
          name: values.name,
          type: values.type,
        };
        await createEntity(newEntity).unwrap();
        toast({
          title: <SuccessToastMessage message={t('entities.entityAdded')} />,
        });
      }
      onClose();
      form.reset();
    } catch (error) {
      console.error(`Failed to ${entity ? 'update' : 'create'} entity:`, error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex gap-3">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <FloatingField
                    id="entityName"
                    label={t('entities.entityNamePlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <FloatingField
                    id="entityType"
                    label={t('entities.selectType')}
                    className="capitalize h-full"
                    as="select"
                    options={Object.values(EntityType).map(type => ({
                      label: type.toLowerCase(),
                      value: type,
                      className: 'capitalize',
                    }))}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {form.watch('type') === EntityType.REGEX && (
          <FormField
            control={form.control}
            name="metadata.value"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    id="regexValue"
                    label={t('entities.regexValuePlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <RenderButtons
          handleClose={onClose}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!entity}
        />
      </form>
    </Form>
  );
};

export default AddEntityForm;
